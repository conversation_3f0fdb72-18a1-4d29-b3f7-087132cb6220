import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'config/supabase_config.dart';
import 'services/auth_state_manager.dart';
import 'services/chat_history_service.dart';
import 'services/localization_service.dart';
import 'services/network_aware_supabase.dart';
import 'services/subscription_service.dart';
import 'services/swipe_tracking_service.dart';
import 'services/image_cache_manager.dart';
import 'services/image_preloading_service.dart';
import 'widgets/auth_wrapper.dart';
import 'utils/oauth_debug_helper.dart';
import 'generated/l10n/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set up global error handling for unhandled exceptions
  FlutterError.onError = (FlutterErrorDetails details) {
    if (kDebugMode) {
      print('Flutter Error: ${details.exception}');
      print('Stack trace: ${details.stack}');
    }
    // Don't crash the app, just log the error
  };

  // Handle errors in async operations that aren't caught
  PlatformDispatcher.instance.onError = (error, stack) {
    if (kDebugMode) {
      print('Unhandled async error: $error');
      print('Stack trace: $stack');
    }
    // Return true to indicate the error was handled
    return true;
  };

  try {
    // Initialize Supabase
    await SupabaseConfig.initialize();

    // Initialize Network-Aware Supabase wrapper
    await NetworkAwareSupabase().initialize();

    // Initialize Localization Service
    await LocalizationService().initialize();

    // Initialize Chat History Service asynchronously to prevent blocking
    ChatHistoryService.initialize().catchError((error) {
      if (kDebugMode) {
        print('Chat History Service initialization failed: $error');
      }
      // Return null to prevent unhandled exception
      return null;
    });

    // Initialize Subscription Service asynchronously
    SubscriptionService.initialize().catchError((error) {
      if (kDebugMode) {
        print('Subscription Service initialization failed: $error');
      }
      // Return null to prevent unhandled exception
      return null;
    });

    // Initialize Swipe Tracking Service asynchronously
    SwipeTrackingService.initialize().catchError((error) {
      if (kDebugMode) {
        print('Swipe Tracking Service initialization failed: $error');
      }
      // Return null to prevent unhandled exception
      return null;
    });

    // Initialize Image Cache Manager
    ImageCacheManager().initialize();

    // Initialize Image Preloading Service
    await ImagePreloadingService().initialize();

    if (kDebugMode) {
      print('Main: App initialization successful');
    }
  } catch (error) {
    if (kDebugMode) {
      print('Main: App initialization error: $error');
      print('Main: Continuing with limited functionality...');
    }
    // Continue running the app even if initialization fails
    // The app can handle errors gracefully in individual services
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: LocalizationService(),
      builder: (context, child) {
        return MaterialApp(
          title: 'TripWiseGo',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: const Color(0xFF0D76FF),
              surfaceTint: const Color(0xFFF7F9FC),
            ),
            useMaterial3: true,
            // Apply surface tint to dialog theme
            dialogTheme: const DialogTheme(
              surfaceTintColor: Color(0xFFF7F9FC),
            ),
            // Apply surface tint to app bar theme
            appBarTheme: const AppBarTheme(
              surfaceTintColor: Color(0xFFF7F9FC),
            ),
            // Apply surface tint to bottom sheet theme
            bottomSheetTheme: const BottomSheetThemeData(
              surfaceTintColor: Color(0xFFF7F9FC),
            ),
            // Apply surface tint to card theme
            cardTheme: const CardTheme(
              surfaceTintColor: Color(0xFFF7F9FC),
            ),
            // Apply surface tint to popup menu theme
            popupMenuTheme: const PopupMenuThemeData(
              surfaceTintColor: Color(0xFFF7F9FC),
            ),
          ),
          // Localization configuration
          locale: LocalizationService().currentLocale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          // RTL support
          builder: (context, child) {
            final locale = Localizations.localeOf(context);
            final isRTL = LocalizationService.isRTL(locale.languageCode);
            return Directionality(
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              child: child!,
            );
          },
          home: const SplashScreen(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize auth state manager asynchronously but don't block UI
      AuthStateManager().initialize().catchError((error) {
        if (kDebugMode) {
          print('Auth state manager initialization failed: $error');
        }
      });

      // Initialize OAuth debug helper in debug mode
      if (kDebugMode) {
        OAuthDebugHelper.validateOAuthConfig();
        OAuthDebugHelper.startAuthStateMonitoring();
      }

      // Wait minimum 2 seconds for splash screen visibility
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const AuthWrapper(),
          ),
        );
      }
    } catch (error) {
      if (kDebugMode) {
        print('App initialization error: $error');
      }

      // Continue to AuthWrapper even if initialization fails
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const AuthWrapper(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D76FF), // #0D76FF color
      body: Center(
        child: Image.asset(
          'assets/images/logo.webp',
          width: 200, // Adjust size as needed
          height: 200, // Adjust size as needed
        ),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text(
              'You have pushed the button this many times:',
            ),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
