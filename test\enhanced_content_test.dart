import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/ai_itinerary_parser.dart';

void main() {
  group('Enhanced Content Parsing Tests', () {
    test('should parse table content correctly', () {
      const tableResponse = '''
Here's a quick comparison table for your trip:

| Destination | Best Time | Budget | Duration |
|-------------|-----------|--------|----------|
| Paris | Spring | \$2000 | 5 days |
| Tokyo | Fall | \$3000 | 7 days |
| Bali | Summer | \$1500 | 6 days |

This table shows the key details for each destination.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(tableResponse);

      // The content might be detected as table, mixed, or itinerary depending on parsing logic
      expect(parsedContent.type,
          isIn([ContentType.table, ContentType.mixed, ContentType.itinerary]));
      expect(parsedContent.rawContent, contains('Destination'));
      expect(parsedContent.rawContent, contains('Paris'));
      expect(parsedContent.rawContent, contains('Tokyo'));
      expect(parsedContent.rawContent, contains('Bali'));
    });

    test('should parse itinerary content correctly', () {
      const itineraryResponse = '''
# 5-Day Paris Itinerary

**Day 1: Arrival and City Center**
- Check into hotel
- Visit Notre-Dame Cathedral
- Walk along the Seine River
- Dinner at a local bistro

**Day 2: Museums and Culture**
- Morning at the Louvre Museum
- Afternoon at Musée d'Orsay
- Evening stroll through Montmartre

**Day 3: Iconic Landmarks**
- Visit the Eiffel Tower
- Explore Champs-Élysées
- Arc de Triomphe
- Shopping at Galeries Lafayette
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(itineraryResponse);

      expect(parsedContent.type, ContentType.itinerary);
      expect(parsedContent.itinerary, isNotNull);

      final itinerary = parsedContent.itinerary!;
      expect(itinerary.title, contains('Paris'));
      expect(itinerary.daySpecificActivities, isNotNull);
      expect(itinerary.daySpecificActivities!.keys.length, greaterThan(0));
    });

    test('should parse list content correctly', () {
      const listResponse = '''
Here are the top travel tips for your trip:

• Pack light and bring versatile clothing
• Always carry a portable charger
• Download offline maps before traveling
• Keep copies of important documents
• Learn basic phrases in the local language

Additional recommendations:
- Book accommodations in advance
- Research local customs and etiquette
- Consider travel insurance
- Exchange currency before departure
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(listResponse);

      expect(parsedContent.type, ContentType.list);
      expect(parsedContent.lists, isNotNull);
      expect(parsedContent.lists!.length, greaterThan(0));
    });

    test('should handle mixed content correctly', () {
      const mixedResponse = '''
# Travel Planning Guide

Here's a comparison of destinations:

| City | Cost | Rating |
|------|------|--------|
| Paris | High | 5/5 |
| Rome | Medium | 4/5 |

**Packing Checklist:**
• Passport and visa
• Travel insurance documents
• Comfortable walking shoes
• Weather-appropriate clothing

**Day 1 Itinerary:**
- Morning: Arrival and hotel check-in
- Afternoon: City walking tour
- Evening: Welcome dinner
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(mixedResponse);

      // Should detect as mixed content or the most prominent type
      expect(parsedContent.type,
          isIn([ContentType.mixed, ContentType.table, ContentType.itinerary]));
      expect(parsedContent.title, isNotEmpty);
    });

    test('should handle plain content correctly', () {
      const plainResponse = '''
Paris is a beautiful city with rich history and culture. 
The best time to visit is during spring when the weather is mild.
You should definitely try the local cuisine and visit the major landmarks.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(plainResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.title, isNotEmpty);
      expect(parsedContent.rawContent, equals(plainResponse));
    });
  });

  group('Integration Tests', () {
    test('should handle empty content', () {
      const emptyResponse = '';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(emptyResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.rawContent, equals(emptyResponse));
    });

    test('should handle content with special characters', () {
      const specialResponse = '''
Travel costs in €:
• Paris: €100/day
• Rome: €80/day
• Barcelona: €70/day
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(specialResponse);

      expect(parsedContent.type, isIn([ContentType.list, ContentType.plain]));
      expect(parsedContent.rawContent, contains('€'));
    });

    test('should parse user itinerary example with \$1 placeholders', () {
      const userItineraryExample = '''
Alright, a 5-day Bali itinerary! Let's craft something amazing for you. This itinerary balances cultural immersion, natural beauty, and a touch of relaxation, with options to tailor it to your interests. I've included estimated costs (USD) for activities and meals, but these can vary greatly depending on your choices.

\$1 A blend of culture, nature, and relaxation.

\$1 I'm assuming you'll base yourself in Ubud (central, cultural hub) and Seminyak (beach/nightlife). You can adjust this based on your preference.

\$1

*   \$1 Arrive at Ngurah Rai International Airport (DPS) in Denpasar. Arrange a pre-booked airport transfer to Ubud (approx. 1.5 - 2 hours).
*   \$1 Check into your Ubud accommodation. I recommend a guesthouse or boutique hotel with a pool.
*   \$1 Explore Ubud's center. Visit the Ubud Art Market for souvenirs (bargain hard!).
\$1warung\$1Babi Guling\$1Nasi Goreng* (fried rice). Consider a traditional Balinese dance performance.

\$1

*   \$1 Visit the Tegalalang Rice Terraces. Take a walk through the terraces, enjoy the views, and take some photos. (Entrance fee: ~\$2)
*   \$1 Explore Tirta Empul Temple, a significant Hindu water temple. Participate in a purification ritual if you wish (dress modestly – sarong and sash required, often available for rent). (Entrance fee: ~\$3)
*   \$1 Head to the Sacred Monkey Forest Sanctuary. Be mindful of your belongings – the monkeys are curious! (Entrance fee: ~\$8)
*   \$1 Cooking class! Learn to prepare authentic Balinese dishes. Many options are available in Ubud.

\$1

*   \$1 Early start! Hire a driver (or join a tour) to visit Mount Batur. Hike to the summit for sunrise (challenging, approx. 2 hours). Alternatively, enjoy the views from a viewpoint.
\$1Kopi Luwak* (civet coffee) and sample different coffees and teas.
*   \$1 Relax and rejuvenate with a Balinese massage. Ubud is known for its excellent spas.
\$1warung* or restaurant.

\$1

*   \$1 Transfer from Ubud to Seminyak (approx. 1.5 - 2 hours). Check into your Seminyak accommodation.
*   \$1 Relax on Seminyak Beach. Rent a sunbed, swim in the ocean, or try surfing.
*   \$1 Explore Seminyak's shops and boutiques.
*   \$1 Sunset cocktails at a beach club (Potato Head, Ku De Ta, or La Plancha are popular choices). Dinner at a stylish Seminyak restaurant.

\$1

*   \$1 Relax on the beach or enjoy a final Balinese breakfast.
*   \$1 Visit Uluwatu Temple, perched on a cliff overlooking the ocean. Witness the Kecak Fire Dance performance at sunset (highly recommended!). (Entrance fee: ~\$10, Kecak Dance: ~\$10-15)
*   \$1 Transfer to Ngurah Rai International Airport (DPS) for your departure.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(userItineraryExample);

      // Should be detected as itinerary content
      expect(parsedContent.type, ContentType.itinerary);
      expect(parsedContent.rawContent, contains('5-day Bali itinerary'));
      expect(parsedContent.itinerary, isNotNull);
      if (parsedContent.itinerary != null) {
        expect(parsedContent.itinerary!.destinations, isNotEmpty);
      }
    });

    test('should parse user table example', () {
      const userTableExample = '''
Okay, let's figure out the best time for \$1 Thailand adventure! Thailand has a wonderfully diverse climate, so "best" really depends on what you want to experience and which regions you're planning to visit. Here's a breakdown, keeping in mind Thailand generally has three seasons: hot, rainy, and cool.

| Season | Months      | Temperature | Rainfall | Crowds | Notes                                  |
| :----- | :---------- | :---------- | :------- | :----- | :------------------------------------- |
| Hot    | March-May   | 30-35°C      | Low      | Moderate | Hottest, best for beaches              |
| Rainy  | June-October | 27-32°C      | High     | Low    | Lush landscapes, fewer tourists         |
| Cool   | Nov-Feb     | 20-30°C      | Low      | High    | Pleasant, popular, festivals, peak season |
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(userTableExample);

      // Should be detected as table content
      expect(parsedContent.type, ContentType.table);
      expect(parsedContent.rawContent, contains('Thailand'));
      expect(parsedContent.tables, isNotNull);
      expect(parsedContent.tables!.length, greaterThan(0));
    });
  });
}
